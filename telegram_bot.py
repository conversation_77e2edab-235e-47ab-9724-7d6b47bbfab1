#!/usr/bin/env python3
"""
Telegram Bot Runner - Main entry point for Telegram bot
"""

import asyncio
import logging
import signal
import sys
import platform
from typing import Optional

from logging_config import setup_logging, important_logger
from utils.config import get_telegram_token, get_telegram_enabled
from services.telegram.telegram_service import get_telegram_service
from handlers.telegram.telegram_commands import get_telegram_command_handler

logger = logging.getLogger(__name__)

class TelegramBotRunner:
    """Main Telegram bot runner"""
    
    def __init__(self):
        self.telegram_service = get_telegram_service()
        self.command_handler = get_telegram_command_handler()
        self.is_running = False
    
    async def initialize(self):
        """Initialize the Telegram bot"""
        if not get_telegram_enabled():
            logger.warning("Telegram bot is disabled in config")
            return False
            
        if not get_telegram_token():
            logger.error("Telegram bot token not found in config")
            return False
        
        # Initialize Telegram service
        if not await self.telegram_service.initialize():
            logger.error("Failed to initialize Telegram service")
            return False
        
        # Register command handlers
        self.telegram_service.register_command_handler(
            'watchlist', 
            self.command_handler.handle_watchlist_command
        )
        
        logger.info("Telegram bot initialized successfully")
        return True
    
    async def start(self):
        """Start the Telegram bot"""
        if not await self.initialize():
            return False
        
        try:
            self.is_running = True
            important_logger.info("🤖 Starting Telegram bot...")
            
            # Start polling
            await self.telegram_service.start_polling()
            
            important_logger.info("✅ Telegram bot started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error starting Telegram bot: {e}")
            important_logger.error(f"❌ Failed to start Telegram bot: {e}")
            return False
    
    async def stop(self):
        """Stop the Telegram bot"""
        if self.is_running:
            try:
                important_logger.info("🛑 Stopping Telegram bot...")
                await self.telegram_service.stop_polling()
                self.is_running = False
                important_logger.info("✅ Telegram bot stopped successfully")
            except Exception as e:
                logger.error(f"Error stopping Telegram bot: {e}")
                important_logger.error(f"❌ Error stopping Telegram bot: {e}")

# Global bot instance
telegram_bot_runner: Optional[TelegramBotRunner] = None

async def shutdown():
    """Graceful shutdown"""
    global telegram_bot_runner
    
    if telegram_bot_runner:
        await telegram_bot_runner.stop()
    
    important_logger.info("🔄 Telegram bot shutdown complete")

async def main():
    """Main entry point"""
    global telegram_bot_runner
    
    try:
        # Setup logging
        setup_logging()
        important_logger.info("🚀 Starting Telegram Bot Runner...")
        
        # Create bot runner
        telegram_bot_runner = TelegramBotRunner()
        
        # Setup signal handlers for graceful shutdown
        def signal_handler(sig_name):
            important_logger.info(f"Received {sig_name}, shutting down...")
            asyncio.create_task(shutdown())
        
        if platform.system() != 'Windows':
            loop = asyncio.get_event_loop()
            for sig in [signal.SIGINT, signal.SIGTERM, signal.SIGHUP]:
                loop.add_signal_handler(sig, lambda s=sig: signal_handler(s.name))
        
        # Start the bot
        if await telegram_bot_runner.start():
            # Keep running until interrupted
            try:
                while telegram_bot_runner.is_running:
                    await asyncio.sleep(1)
            except KeyboardInterrupt:
                important_logger.info("Received keyboard interrupt")
                await shutdown()
        else:
            important_logger.error("❌ Failed to start Telegram bot")
            sys.exit(1)
    
    except Exception as e:
        logger.error(f"Fatal error in Telegram bot: {e}")
        important_logger.error(f"💥 Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Telegram bot stopped by user")
    except Exception as e:
        print(f"💥 Fatal error: {e}")
        sys.exit(1)
