# ChartFix Bot (Discord + Telegram)

## System Overview

ChartFix is a comprehensive bot designed for cryptocurrency market monitoring and trading with both Discord and Telegram integration. The bot provides real-time market data, trading alerts, and trading capabilities through both platforms.

### Primary Purpose

The system helps users monitor cryptocurrency markets and execute trades through Discord commands, with a focus on providing timely market information and trading capabilities.

## Project Structure

```
📁 ChartFix Discord Bot
├── 🤖 bot.py                         # Main application entry point
├── 📝 logging_config.py              # Logging configuration
│
├── 📁 handlers/
│   └── 📁 discord/                  # Discord command handlers
│       ├── 📁 admin/                 # Admin commands
│       ├── 📁 alerts/                # Alert handlers
│       │   ├── trading_time_alerts.py
│       │   └── price_alert_handler.py
│       ├── 📁 market/                # Market data commands
│       └── 📁 trading/               # Trading commands
│
├── 📁 services/                     # Business logic
│   ├── 📁 core/                      # Core utilities
│   ├── 📁 data/                      # Data persistence
│   ├── 📁 discord/                   # Discord integration
│   ├── 📁 market/                    # Market data services
│   └── 📁 trading/                   # Trading services
│
├── 📁 utils/                        # Utility functions
│   ├── config.py                    # Configuration management
│   ├── constants.py                 # Application constants
│   ├── symbol_mappings.py           # Symbol mapping
│   └── ui_components.py             # UI formatting
│
└── 📁 logs/                         # Application logs
```

## Core Features

### Market Monitoring
- Real-time price tracking
- Price alerts with configurable thresholds
- Trading time alerts

### Trading Commands
- Basic trading functionality
- Position management
- Price alerts

## Technology Stack

### Core Framework
- **Python 3.8+**: Asynchronous programming with asyncio
- **Discord.py**: Discord API wrapper
- **python-telegram-bot**: Telegram API wrapper
- **SQLite**: Local database for data persistence

### External APIs
- **Binance API**: Cryptocurrency market data
- **Other APIs**: As implemented in services

## Getting Started

### Prerequisites
- Python 3.8 or higher
- Discord Bot Token
- Telegram Bot Token (optional, for Telegram integration)
- Required Python packages (install via `pip install -r requirements.txt`)

### Installation
1. Clone the repository
2. Install dependencies: `pip install -r requirements.txt`
3. Configure `config.yaml` with your settings
4. Run the bot: `python bot.py`

## Configuration

Edit `config.yaml` to configure:

### Discord Configuration
- Discord bot token
- Guild ID and Admin ID

### Telegram Configuration (Optional)
- Telegram bot token
- Chat ID (set automatically when user starts the bot)
- Enable/disable Telegram integration

### Other Settings
- API keys (Binance, etc.)
- Alert thresholds
- Market monitoring settings

### Telegram Setup
1. Create a bot with @BotFather on Telegram
2. Get your bot token
3. Add the token to `config.yaml`
4. Send `/start` to your bot to set up chat ID
5. Use `/watchlist` to get market data

## Contributing

Contributions are welcome! Please follow the existing code style and submit pull requests.


