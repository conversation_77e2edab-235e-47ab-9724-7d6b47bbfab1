#!/usr/bin/env python3
"""
Integrated Bot Runner - Runs both Discord and Telegram bots together
"""

import asyncio
import logging
import signal
import sys
import platform
from typing import Optional

from logging_config import setup_logging, important_logger
from utils.config import get_discord_token, get_telegram_enabled

# Import Discord bot
from bot import CryptoBot

# Import Telegram bot
from services.telegram.telegram_service import get_telegram_service
from handlers.telegram.telegram_commands import get_telegram_command_handler

logger = logging.getLogger(__name__)

class IntegratedBotRunner:
    """Runs both Discord and Telegram bots"""
    
    def __init__(self):
        self.discord_bot: Optional[CryptoBot] = None
        self.telegram_service = get_telegram_service()
        self.telegram_command_handler = get_telegram_command_handler()
        self.is_running = False
    
    async def initialize_telegram(self):
        """Initialize Telegram bot if enabled"""
        if not get_telegram_enabled():
            logger.info("Telegram bot is disabled")
            return True
        
        try:
            # Initialize Telegram service
            if not await self.telegram_service.initialize():
                logger.error("Failed to initialize Telegram service")
                return False
            
            # Register command handlers
            self.telegram_service.register_command_handler(
                'watchlist', 
                self.telegram_command_handler.handle_watchlist_command
            )
            
            logger.info("Telegram bot initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing Telegram bot: {e}")
            return False
    
    async def start(self):
        """Start both Discord and Telegram bots"""
        try:
            self.is_running = True
            important_logger.info("🚀 Starting Integrated Bot (Discord + Telegram)...")
            
            # Initialize Discord bot
            discord_token = get_discord_token()
            if not discord_token:
                logger.error("Discord bot token not found")
                return False
            
            self.discord_bot = CryptoBot()
            
            # Initialize Telegram bot
            telegram_initialized = await self.initialize_telegram()
            
            # Start both bots concurrently
            tasks = []
            
            # Discord bot task
            discord_task = asyncio.create_task(
                self.discord_bot.start(discord_token),
                name="discord_bot"
            )
            tasks.append(discord_task)
            
            # Telegram bot task (if enabled)
            if telegram_initialized and get_telegram_enabled():
                telegram_task = asyncio.create_task(
                    self.telegram_service.start_polling(),
                    name="telegram_bot"
                )
                tasks.append(telegram_task)
                important_logger.info("✅ Both Discord and Telegram bots started")
            else:
                important_logger.info("✅ Discord bot started (Telegram disabled)")
            
            # Wait for any task to complete (shouldn't happen in normal operation)
            done, pending = await asyncio.wait(tasks, return_when=asyncio.FIRST_COMPLETED)
            
            # If we reach here, one of the bots stopped unexpectedly
            for task in pending:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
            
            # Check which task completed and why
            for task in done:
                try:
                    result = await task
                    logger.warning(f"Task {task.get_name()} completed unexpectedly: {result}")
                except Exception as e:
                    logger.error(f"Task {task.get_name()} failed: {e}")
            
            return False
            
        except Exception as e:
            logger.error(f"Error starting integrated bot: {e}")
            important_logger.error(f"❌ Failed to start integrated bot: {e}")
            return False
    
    async def stop(self):
        """Stop both bots"""
        if self.is_running:
            try:
                important_logger.info("🛑 Stopping integrated bot...")
                
                # Stop Discord bot
                if self.discord_bot:
                    await self.discord_bot.close()
                    logger.info("Discord bot stopped")
                
                # Stop Telegram bot
                if get_telegram_enabled():
                    await self.telegram_service.stop_polling()
                    logger.info("Telegram bot stopped")
                
                self.is_running = False
                important_logger.info("✅ Integrated bot stopped successfully")
                
            except Exception as e:
                logger.error(f"Error stopping integrated bot: {e}")
                important_logger.error(f"❌ Error stopping integrated bot: {e}")

# Global bot instance
integrated_bot_runner: Optional[IntegratedBotRunner] = None

async def shutdown():
    """Graceful shutdown"""
    global integrated_bot_runner
    
    if integrated_bot_runner:
        await integrated_bot_runner.stop()
    
    important_logger.info("🔄 Integrated bot shutdown complete")

async def main():
    """Main entry point"""
    global integrated_bot_runner
    
    try:
        # Setup logging
        setup_logging()
        important_logger.info("🚀 Starting Integrated Bot Runner...")
        
        # Create bot runner
        integrated_bot_runner = IntegratedBotRunner()
        
        # Setup signal handlers for graceful shutdown
        def signal_handler(sig_name):
            important_logger.info(f"Received {sig_name}, shutting down...")
            asyncio.create_task(shutdown())
        
        if platform.system() != 'Windows':
            loop = asyncio.get_event_loop()
            for sig in [signal.SIGINT, signal.SIGTERM, signal.SIGHUP]:
                loop.add_signal_handler(sig, lambda s=sig: signal_handler(s.name))
        
        # Start the integrated bot
        await integrated_bot_runner.start()
    
    except KeyboardInterrupt:
        important_logger.info("Received keyboard interrupt")
        await shutdown()
    except Exception as e:
        logger.error(f"Fatal error in integrated bot: {e}")
        important_logger.error(f"💥 Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Integrated bot stopped by user")
    except Exception as e:
        print(f"💥 Fatal error: {e}")
        sys.exit(1)
