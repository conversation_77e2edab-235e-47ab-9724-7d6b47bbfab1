
discord:
  token: MTM3NTg4MDExNTQ1MTMzNDY3Ng.GD5Gs6.oiezpSQmfsQHICzVUdWYPkx_7a3SJGXGZKIaXY
  guild_id: '1375879723296489562'
  admin_id: '1365018104249450540'
  command_prefix: '!'

telegram:
  token: **********************************************
  chat_id: ''  # Will be set when user starts the bot
  enabled: true

database:
  path: chartfix.db
  backup_enabled: true
  connection_pool_size: 10

binance:
  api_key: QW6QRegbbpjnE9u1GSbNqfUXP1oQvYUHUQmQW0yFwE62q2CaC0bb2gq3QKUnbLR7
  api_secret: Y6uI8iW6fcvisaYtUD44zFjhzJs5W2ornv3T68brPVxF50EYzfn0vhFRiyBbzCB3
  testnet: false

market:
  watchlist_symbols:
    - BTCUSDT
    - ETHUSDT
    - BNBUSDT
    - SOLUSDT
    - NEARUSDT
    - LINKUSDT
    - ENAUSDT
    - DOGEUSDT
    - XRPUSDT
    - WLDUSDT
  price_refresh_interval: 90        # seconds
  market_overview_refresh_interval: 900  # seconds

market_monitor:
  p2p_increase_thresholds:
    - 2
    - 5
    - 10
  earn_fixed_thresholds:
    - 10
    - 15
    - 20
    - 25

price_alerts:
  enabled: true
  channel_name: "\U0001F6A8-alerts"

  daily_change_thresholds:
    - 5
    - 10
    - 15

  short_term_alerts:
    enabled: true
    monitoring_interval: 300  # seconds
    timeframes:
      1h:
        enabled: true
        thresholds: [3, 5, 8]
      4h:
        enabled: true
        thresholds: [4, 7, 10]

  price_targets:
    BTCUSDT: [89000.0, 92000.0, 96000.0, 100000.0, 103500.0, 106000.0, 111000.0]
    ETHUSDT: [1800.0]
    BNBUSDT: [600.0]
    SOLUSDT: [110.0]
    NEARUSDT: [1.9]
    LINKUSDT: [11.69]
    DOGEUSDT: [0.135, 0.155]
    WLDUSDT: [0.69]
# Volume Alerts Configuration
volume_alerts:
  enabled: true
  channel_name: "\U0001F6A8-alerts"
  monitoring_interval: 300  # seconds
  ma_period: 20            # Moving average period
  thresholds: [1.8, 2.2]  # Volume/MA ratios

# Trading Time Analysis
trading_time_analysis:
  monitoring_interval: 300
  price_movement_threshold: 5
  volume_surge_threshold: 200
  top_coins_limit: 100
  alert_thresholds:
    price_movement_levels: [5, 10, 15]
    volume_surge_levels: [200, 500, 1000]

economic_calendar:
  forex_factory_base_url: https://nfs.faireconomy.media
  currency_filter:
    - USD
    - CNY
    - JPY

# Market News APIs
market_news_apis:
  newsapi_key: ********************************
  alpha_vantage_key: QIGGKH7XB6W9R8BN

xau_data:
  enabled: true
  cache_ttl: 30                   # seconds - sync with watchlist refresh
  source: "yahoo_finance"         # primary data source
  symbol: "XAUUSD"               # trading symbol
  refresh_with_watchlist: true   # refresh when watchlist updates
