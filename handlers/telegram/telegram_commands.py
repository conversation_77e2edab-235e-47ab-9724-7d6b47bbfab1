"""
Telegram Command Handlers - Handle commands from Telegram
"""

import logging
import time
from typing import Dict, Any
from telegram import Update
from telegram.ext import ContextTypes
from telegram.error import TelegramError

from services.market.market_service import get_market_service
from services.market.market_monitor_service import get_market_monitor_service
from services.market.mt5_data_service import get_mt5_service
from utils.ui_components import format_price_enhanced, format_percentage_enhanced

logger = logging.getLogger(__name__)

class TelegramCommandHandler:
    """Handler for Telegram commands"""

    def __init__(self):
        self.market_service = get_market_service()
        self.monitor_service = get_market_monitor_service()
        self.mt5_service = get_mt5_service()
        self.previous_prices = {}

        # Store pinned watchlist message ID for auto-refresh
        self.pinned_watchlist_message_id = None
        self.last_watchlist_update = None
    
    async def handle_watchlist_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /watchlist command"""
        try:
            # Send typing indicator
            await update.message.reply_chat_action('typing')
            
            # Get watchlist data
            watchlist_data = await self.market_service.get_watchlist_data()
            
            if not watchlist_data['success']:
                await update.message.reply_text(
                    f"❌ Lỗi khi lấy dữ liệu: {watchlist_data['error']}"
                )
                return
            
            # Calculate price changes
            watchlist_symbols = watchlist_data.get('data', {})
            current_prices = {}

            for symbol, data in watchlist_symbols.items():
                if isinstance(data, dict) and 'price' in data:
                    current_prices[symbol] = data['price']
                else:
                    logger.warning(f"Invalid data structure for symbol {symbol}: {data}")
                    current_prices[symbol] = 0.0
            
            price_changes = {}
            for symbol, current_price in current_prices.items():
                if symbol in self.previous_prices:
                    previous_price = self.previous_prices[symbol]
                    change = ((current_price - previous_price) / previous_price) * 100
                    price_changes[symbol] = change
                else:
                    price_changes[symbol] = 0.0
            
            self.previous_prices.update(current_prices)
            
            # Format watchlist message
            message = await self.format_watchlist_message(watchlist_data, price_changes)

            # Send message and pin it
            sent_message = await update.message.reply_text(message, parse_mode='Markdown')

            # Pin the message and store message ID for auto-refresh
            if sent_message:
                try:
                    await update.message.get_bot().pin_chat_message(
                        chat_id=update.effective_chat.id,
                        message_id=sent_message.message_id,
                        disable_notification=True
                    )
                    self.pinned_watchlist_message_id = sent_message.message_id
                    self.last_watchlist_update = time.time()
                    logger.info(f"Pinned watchlist message {sent_message.message_id}")
                except Exception as e:
                    logger.warning(f"Failed to pin watchlist message: {e}")

            logger.info(f"Watchlist command executed for Telegram user {update.effective_user.id}")
            
        except Exception as e:
            logger.error(f"Error in Telegram watchlist command: {e}")
            await update.message.reply_text(
                "❌ Có lỗi xảy ra khi lấy dữ liệu watchlist. Vui lòng thử lại sau."
            )

    async def auto_refresh_watchlist(self):
        """Auto-refresh pinned watchlist message every 90 seconds"""
        if not self.pinned_watchlist_message_id:
            return

        try:
            # Get fresh watchlist data
            watchlist_data = await self.market_service.get_watchlist_data()

            if not watchlist_data['success']:
                logger.warning(f"Failed to get watchlist data for auto-refresh: {watchlist_data['error']}")
                return

            # Calculate price changes
            current_prices = {}
            watchlist_symbols = watchlist_data.get('data', {})

            for symbol, data in watchlist_symbols.items():
                if isinstance(data, dict) and 'price' in data:
                    current_prices[symbol] = data['price']
                else:
                    current_prices[symbol] = 0.0

            price_changes = {}
            for symbol, current_price in current_prices.items():
                if symbol in self.previous_prices:
                    previous_price = self.previous_prices[symbol]
                    if previous_price > 0:
                        change = ((current_price - previous_price) / previous_price) * 100
                        price_changes[symbol] = change
                    else:
                        price_changes[symbol] = 0.0
                else:
                    price_changes[symbol] = 0.0

            self.previous_prices.update(current_prices)

            # Format updated message
            message = await self.format_watchlist_message(watchlist_data, price_changes)

            # Edit the pinned message
            from services.telegram.telegram_service import get_telegram_service
            telegram_service = get_telegram_service()

            success = await telegram_service.edit_message(
                self.pinned_watchlist_message_id,
                message
            )

            if success:
                self.last_watchlist_update = time.time()
                logger.debug("Auto-refreshed Telegram watchlist")
            else:
                logger.warning("Failed to auto-refresh Telegram watchlist")

        except Exception as e:
            logger.error(f"Error in auto-refresh watchlist: {e}")
    
    async def format_watchlist_message(self, watchlist_data: Dict[str, Any], price_changes: Dict[str, float]) -> str:
        """Format watchlist data for Telegram message with better alignment"""
        try:
            # Header with better formatting
            message_parts = ["🚀 *CRYPTO WATCHLIST* 📈"]
            message_parts.append("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")

            # Market indicators section
            market_indicators = await self.get_market_indicators()
            if market_indicators:
                message_parts.append("")
                message_parts.append("📊 *Market Indicators*")
                for indicator in market_indicators:
                    message_parts.append(indicator)
                message_parts.append("")

            # Crypto watchlist header
            message_parts.append("💰 *Crypto Prices*")
            message_parts.append("```")
            message_parts.append("Symbol    Price      Change   Volume")
            message_parts.append("────────  ─────────  ───────  ──────")

            watchlist_symbols = watchlist_data.get('data', {})
            for symbol, data in watchlist_symbols.items():
                if not isinstance(data, dict):
                    continue
                price = data['price']
                daily_change = data.get('daily_change_percent', 0)
                volume_24h = data.get('volume_24h', 0)
                
                # Price change indicator
                price_change = price_changes.get(symbol, 0)
                if price_change > 0:
                    change_emoji = "🟢"
                elif price_change < 0:
                    change_emoji = "🔴"
                else:
                    change_emoji = "⚪"
                
                # Daily change indicator
                if daily_change > 0:
                    daily_emoji = "🟢"
                    daily_sign = "+"
                elif daily_change < 0:
                    daily_emoji = "🔴"
                    daily_sign = ""
                else:
                    daily_emoji = "⚪"
                    daily_sign = ""
                
                # Format volume
                if volume_24h >= 1e9:
                    volume_str = f"{volume_24h/1e9:.1f}B"
                elif volume_24h >= 1e6:
                    volume_str = f"{volume_24h/1e6:.1f}M"
                else:
                    volume_str = f"{volume_24h:.0f}"
                
                # Clean symbol name
                clean_symbol = symbol.replace('USDT', '')

                # Format price based on value
                if price >= 1:
                    price_str = f"${price:.2f}"
                else:
                    price_str = f"${price:.4f}"

                # Format change percentage
                change_str = f"{daily_sign}{daily_change:.2f}%"

                # Aligned format for table
                message_parts.append(
                    f"{clean_symbol:<8}  {price_str:>9}  {change_str:>7}  {volume_str:>6}"
                )

            # Close code block and add footer
            message_parts.append("```")
            message_parts.append("")

            # Add emoji indicators with price changes
            price_change_indicators = []
            for symbol, change in price_changes.items():
                if abs(change) > 0.1:  # Only show significant changes
                    clean_symbol = symbol.replace('USDT', '')
                    if change > 0:
                        price_change_indicators.append(f"🟢{clean_symbol}+{change:.1f}%")
                    else:
                        price_change_indicators.append(f"🔴{clean_symbol}{change:.1f}%")

            if price_change_indicators:
                message_parts.append("📈 *Price Changes:* " + " ".join(price_change_indicators[:5]))
                message_parts.append("")

            message_parts.append(f"⏰ _Cập nhật: {time.strftime('%H:%M:%S')}_")

            return "\n".join(message_parts)
            
        except Exception as e:
            logger.error(f"Error formatting watchlist message: {e}")
            return "❌ Lỗi khi định dạng dữ liệu watchlist"
    
    async def get_market_indicators(self) -> list:
        """Get market indicators for display"""
        indicators = []

        try:
            # Get market data from monitor service
            rates_data = await self.monitor_service.get_current_rates()

            if rates_data and not rates_data.get('error'):
                # P2P data
                p2p_data = rates_data.get('p2p', {})
                buy_rate = p2p_data.get('buy_rate', 0)
                if buy_rate > 0:
                    indicators.append(f"💵 P2P: `{buy_rate:,.0f} VND`")

                # APY data
                earn_data = rates_data.get('earn', {})
                flexible_data = earn_data.get('flexible', {})
                apy_rate = flexible_data.get('rate', 0)
                if apy_rate > 0:
                    indicators.append(f"🏦 APY: `{apy_rate:.2f}%`")
            
            # Get XAU data
            try:
                xau_data = await self.mt5_service.get_xau_price()
                if xau_data and xau_data.get('success'):
                    xau_price = xau_data['data'].get('price', 0)
                    xau_change = xau_data['data'].get('change_percent', 0)
                    
                    if xau_change > 0:
                        xau_emoji = "🟢"
                        xau_sign = "+"
                    elif xau_change < 0:
                        xau_emoji = "🔴"
                        xau_sign = ""
                    else:
                        xau_emoji = "⚪"
                        xau_sign = ""
                    
                    indicators.append(
                        f"🥇 XAU: `${xau_price:.2f}` {xau_emoji}`{xau_sign}{xau_change:.2f}%`"
                    )
            except Exception as e:
                logger.debug(f"Could not get XAU data: {e}")
            
        except Exception as e:
            logger.error(f"Error getting market indicators: {e}")
        
        return indicators

# Global instance
_telegram_command_handler = None

def get_telegram_command_handler() -> TelegramCommandHandler:
    """Get the global Telegram command handler instance"""
    global _telegram_command_handler
    if _telegram_command_handler is None:
        _telegram_command_handler = TelegramCommandHandler()
    return _telegram_command_handler
