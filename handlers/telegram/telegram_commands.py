"""
Telegram Command Handlers - Handle commands from Telegram
"""

import logging
import time
from typing import Dict, Any
from telegram import Update
from telegram.ext import ContextTypes
from telegram.error import TelegramError

from services.market.market_service import get_market_service
from services.market.market_monitor_service import get_market_monitor_service
from services.market.mt5_data_service import get_mt5_service
from utils.ui_components import format_price_enhanced, format_percentage_enhanced

logger = logging.getLogger(__name__)

class TelegramCommandHandler:
    """Handler for Telegram commands"""
    
    def __init__(self):
        self.market_service = get_market_service()
        self.monitor_service = get_market_monitor_service()
        self.mt5_service = get_mt5_service()
        self.previous_prices = {}
    
    async def handle_watchlist_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /watchlist command"""
        try:
            # Send typing indicator
            await update.message.reply_chat_action('typing')
            
            # Get watchlist data
            watchlist_data = await self.market_service.get_watchlist_data()
            
            if not watchlist_data['success']:
                await update.message.reply_text(
                    f"❌ Lỗi khi lấy dữ liệu: {watchlist_data['error']}"
                )
                return
            
            # Calculate price changes
            current_prices = {
                symbol: data['price'] 
                for symbol, data in watchlist_data['data'].items()
            }
            
            price_changes = {}
            for symbol, current_price in current_prices.items():
                if symbol in self.previous_prices:
                    previous_price = self.previous_prices[symbol]
                    change = ((current_price - previous_price) / previous_price) * 100
                    price_changes[symbol] = change
                else:
                    price_changes[symbol] = 0.0
            
            self.previous_prices.update(current_prices)
            
            # Format watchlist message
            message = await self.format_watchlist_message(watchlist_data, price_changes)
            
            # Send message
            await update.message.reply_text(message, parse_mode='Markdown')
            
            logger.info(f"Watchlist command executed for Telegram user {update.effective_user.id}")
            
        except Exception as e:
            logger.error(f"Error in Telegram watchlist command: {e}")
            await update.message.reply_text(
                "❌ Có lỗi xảy ra khi lấy dữ liệu watchlist. Vui lòng thử lại sau."
            )
    
    async def format_watchlist_message(self, watchlist_data: Dict[str, Any], price_changes: Dict[str, float]) -> str:
        """Format watchlist data for Telegram message"""
        try:
            # Header
            message_parts = ["🚀 *CRYPTO WATCHLIST* 📈\n"]
            
            # Market indicators section
            market_indicators = await self.get_market_indicators()
            if market_indicators:
                message_parts.append("📊 *Market Indicators*")
                message_parts.extend(market_indicators)
                message_parts.append("")
            
            # Crypto watchlist
            message_parts.append("💰 *Crypto Prices*")
            
            for symbol, data in watchlist_data['data'].items():
                price = data['price']
                daily_change = data.get('daily_change_percent', 0)
                volume_24h = data.get('volume_24h', 0)
                
                # Price change indicator
                price_change = price_changes.get(symbol, 0)
                if price_change > 0:
                    change_emoji = "🟢"
                elif price_change < 0:
                    change_emoji = "🔴"
                else:
                    change_emoji = "⚪"
                
                # Daily change indicator
                if daily_change > 0:
                    daily_emoji = "🟢"
                    daily_sign = "+"
                elif daily_change < 0:
                    daily_emoji = "🔴"
                    daily_sign = ""
                else:
                    daily_emoji = "⚪"
                    daily_sign = ""
                
                # Format volume
                if volume_24h >= 1e9:
                    volume_str = f"{volume_24h/1e9:.1f}B"
                elif volume_24h >= 1e6:
                    volume_str = f"{volume_24h/1e6:.1f}M"
                else:
                    volume_str = f"{volume_24h:.0f}"
                
                # Clean symbol name
                clean_symbol = symbol.replace('USDT', '')
                
                message_parts.append(
                    f"{change_emoji} *{clean_symbol}* `${price:.4f}` "
                    f"{daily_emoji}`{daily_sign}{daily_change:.2f}%` "
                    f"📊`{volume_str}`"
                )
            
            # Footer
            message_parts.append(f"\n⏰ _Cập nhật: {time.strftime('%H:%M:%S')}_")
            
            return "\n".join(message_parts)
            
        except Exception as e:
            logger.error(f"Error formatting watchlist message: {e}")
            return "❌ Lỗi khi định dạng dữ liệu watchlist"
    
    async def get_market_indicators(self) -> list:
        """Get market indicators for display"""
        indicators = []
        
        try:
            # Get P2P and APY data
            p2p_data = await self.monitor_service.get_p2p_rates()
            apy_data = await self.monitor_service.get_earn_rates()
            
            if p2p_data and p2p_data.get('success'):
                buy_rate = p2p_data['data'].get('buy_rate', 0)
                indicators.append(f"💵 P2P: `{buy_rate:,.0f} VND`")
            
            if apy_data and apy_data.get('success'):
                apy_rate = apy_data['data'].get('apy_rate', 0)
                indicators.append(f"🏦 APY: `{apy_rate:.2f}%`")
            
            # Get XAU data
            try:
                xau_data = await self.mt5_service.get_xau_price()
                if xau_data and xau_data.get('success'):
                    xau_price = xau_data['data'].get('price', 0)
                    xau_change = xau_data['data'].get('change_percent', 0)
                    
                    if xau_change > 0:
                        xau_emoji = "🟢"
                        xau_sign = "+"
                    elif xau_change < 0:
                        xau_emoji = "🔴"
                        xau_sign = ""
                    else:
                        xau_emoji = "⚪"
                        xau_sign = ""
                    
                    indicators.append(
                        f"🥇 XAU: `${xau_price:.2f}` {xau_emoji}`{xau_sign}{xau_change:.2f}%`"
                    )
            except Exception as e:
                logger.debug(f"Could not get XAU data: {e}")
            
        except Exception as e:
            logger.error(f"Error getting market indicators: {e}")
        
        return indicators

# Global instance
_telegram_command_handler = None

def get_telegram_command_handler() -> TelegramCommandHandler:
    """Get the global Telegram command handler instance"""
    global _telegram_command_handler
    if _telegram_command_handler is None:
        _telegram_command_handler = TelegramCommandHandler()
    return _telegram_command_handler
