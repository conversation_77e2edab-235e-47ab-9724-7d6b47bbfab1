"""
Telegram Bot Service - Manages Telegram bot connection and message sending
"""

import logging
import asyncio
from typing import Optional, Callable, Dict, Any
from telegram import Bot, Update
from telegram.ext import Application, CommandHandler, ContextTypes
from telegram.error import TelegramError

from utils.config import get_telegram_token, get_telegram_chat_id, set_telegram_chat_id, get_telegram_enabled

logger = logging.getLogger(__name__)

class TelegramService:
    """Service for managing Telegram bot operations"""
    
    def __init__(self):
        self.token = get_telegram_token()
        self.chat_id = get_telegram_chat_id()
        self.enabled = get_telegram_enabled()
        self.bot: Optional[Bot] = None
        self.application: Optional[Application] = None
        self.command_handlers: Dict[str, Callable] = {}
        
        if self.enabled and self.token:
            self.bot = Bot(token=self.token)
            self.application = Application.builder().token(self.token).build()
    
    async def initialize(self):
        """Initialize the Telegram bot"""
        if not self.enabled or not self.token:
            logger.warning("Telegram bot is disabled or token not configured")
            return False
            
        try:
            # Test bot connection
            bot_info = await self.bot.get_me()
            logger.info(f"Telegram bot initialized: @{bot_info.username}")
            return True
        except TelegramError as e:
            logger.error(f"Failed to initialize Telegram bot: {e}")
            return False
    
    def register_command_handler(self, command: str, handler: Callable):
        """Register a command handler"""
        self.command_handlers[command] = handler
        if self.application:
            self.application.add_handler(CommandHandler(command, handler))
            logger.info(f"Registered Telegram command handler: /{command}")
    
    async def send_message(self, text: str, parse_mode: str = 'Markdown') -> bool:
        """Send a message to the configured chat"""
        if not self.enabled or not self.bot or not self.chat_id:
            logger.debug("Telegram messaging not available")
            return False
            
        try:
            await self.bot.send_message(
                chat_id=self.chat_id,
                text=text,
                parse_mode=parse_mode
            )
            logger.debug(f"Sent Telegram message to chat {self.chat_id}")
            return True
        except TelegramError as e:
            logger.error(f"Failed to send Telegram message: {e}")
            return False
    
    async def send_alert(self, alert_type: str, message: str) -> bool:
        """Send an alert message with proper formatting"""
        if not self.enabled:
            return False
            
        # Format alert message
        formatted_message = f"🚨 *{alert_type.upper()}*\n\n{message}"
        return await self.send_message(formatted_message)
    
    async def handle_start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command to set up chat ID"""
        chat_id = str(update.effective_chat.id)
        
        # Save chat ID to config
        if set_telegram_chat_id(chat_id):
            self.chat_id = chat_id
            await update.message.reply_text(
                "✅ *ChartFix Bot đã được kích hoạt!*\n\n"
                "Bạn sẽ nhận được:\n"
                "• 📊 Watchlist crypto\n"
                "• 🚨 Cảnh báo giá\n"
                "• 📈 Cảnh báo volume\n\n"
                "Sử dụng /watchlist để xem danh sách theo dõi",
                parse_mode='Markdown'
            )
            logger.info(f"Telegram chat ID set to: {chat_id}")
        else:
            await update.message.reply_text(
                "❌ Có lỗi khi thiết lập bot. Vui lòng thử lại sau."
            )
    
    async def start_polling(self):
        """Start the Telegram bot polling"""
        if not self.enabled or not self.application:
            logger.warning("Cannot start Telegram polling - bot not configured")
            return
            
        try:
            # Register default handlers
            self.register_command_handler('start', self.handle_start_command)
            
            logger.info("Starting Telegram bot polling...")
            await self.application.initialize()
            await self.application.start()
            await self.application.updater.start_polling()
            
        except Exception as e:
            logger.error(f"Error starting Telegram bot polling: {e}")
    
    async def stop_polling(self):
        """Stop the Telegram bot polling"""
        if self.application:
            try:
                await self.application.updater.stop()
                await self.application.stop()
                await self.application.shutdown()
                logger.info("Telegram bot polling stopped")
            except Exception as e:
                logger.error(f"Error stopping Telegram bot: {e}")

# Global instance
_telegram_service = None

def get_telegram_service() -> TelegramService:
    """Get the global Telegram service instance"""
    global _telegram_service
    if _telegram_service is None:
        _telegram_service = TelegramService()
    return _telegram_service
